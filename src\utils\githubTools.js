import { DynamicTool } from "@langchain/core/tools";
import {
    getUserProfile,
    getUserRepositories,
    getRepository,
    getRepositoryLanguages,
    getRepositoryCommits,
    getUserEvents,
    getUserFollowers,
    getUserFollowing,
    searchRepositories
} from './githubApi.js';

/**
 * Create GitHub API tools for LangChain integration
 * @returns {Array} Array of GitHub tools
 */
export function createGitHubTools() {
    return [
        // Get GitHub Profile Tool
        new DynamicTool({
            name: "getGitHubProfile",
            description: "Get GitHub user profile information including bio, followers, following, public repos count, and other profile details",
            func: async () => {
                try {
                    const username = 'shuvo881';
                    const profile = await getUserProfile(username);
                    
                    return JSON.stringify({
                        username: profile.login,
                        name: profile.name,
                        bio: profile.bio,
                        location: profile.location,
                        company: profile.company,
                        blog: profile.blog,
                        email: profile.email,
                        followers: profile.followers,
                        following: profile.following,
                        public_repos: profile.public_repos,
                        public_gists: profile.public_gists,
                        created_at: profile.created_at,
                        updated_at: profile.updated_at,
                        avatar_url: profile.avatar_url,
                        html_url: profile.html_url
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getGitHubProfile:', error);
                    throw new Error(`Failed to retrieve GitHub profile: ${error.message}`);
                }
            }
        }),

        // Get User Repositories Tool
        new DynamicTool({
            name: "getGitHubRepositories",
            description: "Get list of GitHub repositories for a user with details like name, description, language, stars, forks, and last update",
            func: async (options={}) => {
                try {

                    const username = 'shuvo881';
                    
                    const repos = await getUserRepositories(username, options);
                    
                    const repoSummary = repos.map(repo => ({
                        name: repo.name,
                        full_name: repo.full_name,
                        description: repo.description,
                        language: repo.language,
                        stars: repo.stargazers_count,
                        forks: repo.forks_count,
                        watchers: repo.watchers_count,
                        size: repo.size,
                        created_at: repo.created_at,
                        updated_at: repo.updated_at,
                        pushed_at: repo.pushed_at,
                        html_url: repo.html_url,
                        clone_url: repo.clone_url,
                        topics: repo.topics,
                        is_fork: repo.fork,
                        is_private: repo.private
                    }));

                    return JSON.stringify({
                        total_repositories: repoSummary.length,
                        repositories: repoSummary
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getGitHubRepositories:', error);
                    throw new Error(`Failed to retrieve repositories: ${error.message}`);
                }
            }
        }),

        // Get Repository Details Tool
        new DynamicTool({
            name: "getRepositoryDetails",
            description: "Get detailed information about a specific GitHub repository including languages, recent commits, and statistics",
            func: async (params) => {
                try {
                    const { username = 'shuvo881', repoName } = params;
                    
                    if (!repoName) {
                        throw new Error('Repository name is required');
                    }

                    const [repo, languages, commits] = await Promise.all([
                        getRepository(username, repoName),
                        getRepositoryLanguages(username, repoName).catch(() => ({})),
                        getRepositoryCommits(username, repoName, { per_page: 5 }).catch(() => [])
                    ]);

                    const recentCommits = commits.map(commit => ({
                        sha: commit.sha.substring(0, 7),
                        message: commit.commit.message,
                        author: commit.commit.author.name,
                        date: commit.commit.author.date,
                        url: commit.html_url
                    }));

                    return JSON.stringify({
                        repository: {
                            name: repo.name,
                            full_name: repo.full_name,
                            description: repo.description,
                            language: repo.language,
                            stars: repo.stargazers_count,
                            forks: repo.forks_count,
                            watchers: repo.watchers_count,
                            size: repo.size,
                            created_at: repo.created_at,
                            updated_at: repo.updated_at,
                            pushed_at: repo.pushed_at,
                            html_url: repo.html_url,
                            clone_url: repo.clone_url,
                            topics: repo.topics,
                            license: repo.license?.name,
                            default_branch: repo.default_branch,
                            is_fork: repo.fork,
                            is_private: repo.private
                        },
                        languages: languages,
                        recent_commits: recentCommits
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getRepositoryDetails:', error);
                    throw new Error(`Failed to retrieve repository details: ${error.message}`);
                }
            }
        }),

        // Get GitHub Activity Tool
        new DynamicTool({
            name: "getGitHubActivity",
            description: "Get recent GitHub activity/events for a user including pushes, issues, pull requests, and other activities",
            func: async (params={}) => {
                try {
                    const { username = 'shuvo881', ...options } = params;
                    
                    const events = await getUserEvents(username, options);
                    
                    const activitySummary = events.map(event => ({
                        type: event.type,
                        repo: event.repo?.name,
                        created_at: event.created_at,
                        action: event.payload?.action,
                        ref: event.payload?.ref,
                        commits: event.payload?.commits?.length || 0,
                        public: event.public
                    }));

                    // Group by event type for summary
                    const eventTypeCounts = activitySummary.reduce((acc, event) => {
                        acc[event.type] = (acc[event.type] || 0) + 1;
                        return acc;
                    }, {});

                    return JSON.stringify({
                        total_events: activitySummary.length,
                        event_type_summary: eventTypeCounts,
                        recent_activity: activitySummary.slice(0, 10)
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getGitHubActivity:', error);
                    throw new Error(`Failed to retrieve GitHub activity: ${error.message}`);
                }
            }
        }),

        // Get GitHub Social Info Tool
        new DynamicTool({
            name: "getGitHubSocialInfo",
            description: "Get GitHub social information including followers and following lists",
            func: async (params={}) => {
                try {
                    const { username = 'shuvo881', ...options } = params;
                    
                    const [followers, following] = await Promise.all([
                        getUserFollowers(username, { per_page: 10, ...options }),
                        getUserFollowing(username, { per_page: 10, ...options })
                    ]);

                    const followersSummary = followers.map(user => ({
                        username: user.login,
                        avatar_url: user.avatar_url,
                        html_url: user.html_url,
                        type: user.type
                    }));

                    const followingSummary = following.map(user => ({
                        username: user.login,
                        avatar_url: user.avatar_url,
                        html_url: user.html_url,
                        type: user.type
                    }));

                    return JSON.stringify({
                        followers_count: followersSummary.length,
                        following_count: followingSummary.length,
                        recent_followers: followersSummary,
                        recent_following: followingSummary
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getGitHubSocialInfo:', error);
                    throw new Error(`Failed to retrieve GitHub social info: ${error.message}`);
                }
            }
        }),

        // Search Repositories Tool
        new DynamicTool({
            name: "searchGitHubRepositories",
            description: "Search GitHub repositories by query with filters for language, stars, topics, etc.",
            func: async (params) => {
                try {
                    const { query, ...options } = params;
                    
                    if (!query) {
                        throw new Error('Search query is required');
                    }

                    const searchResults = await searchRepositories(query, options);
                    
                    const repoSummary = searchResults.items.map(repo => ({
                        name: repo.name,
                        full_name: repo.full_name,
                        description: repo.description,
                        language: repo.language,
                        stars: repo.stargazers_count,
                        forks: repo.forks_count,
                        updated_at: repo.updated_at,
                        html_url: repo.html_url,
                        topics: repo.topics,
                        owner: repo.owner.login
                    }));

                    return JSON.stringify({
                        total_count: searchResults.total_count,
                        incomplete_results: searchResults.incomplete_results,
                        repositories: repoSummary
                    }, null, 2);
                } catch (error) {
                    console.error('Error in searchGitHubRepositories:', error);
                    throw new Error(`Failed to search repositories: ${error.message}`);
                }
            }
        }),

        // Get Repository Statistics Tool
        new DynamicTool({
            name: "getRepositoryStatistics",
            description: "Get comprehensive statistics for GitHub repositories including language breakdown, commit frequency, and top repositories",
            func: async () => {
                try {
                    const username = 'shuvo881';
                    const repos = await getUserRepositories(username, { per_page: 100 });
                    
                    // Language statistics
                    const languageStats = {};
                    let totalStars = 0;
                    let totalForks = 0;
                    let totalSize = 0;

                    repos.forEach(repo => {
                        if (repo.language) {
                            languageStats[repo.language] = (languageStats[repo.language] || 0) + 1;
                        }
                        totalStars += repo.stargazers_count;
                        totalForks += repo.forks_count;
                        totalSize += repo.size;
                    });

                    // Top repositories by stars
                    const topRepos = repos
                        .filter(repo => !repo.fork)
                        .sort((a, b) => b.stargazers_count - a.stargazers_count)
                        .slice(0, 5)
                        .map(repo => ({
                            name: repo.name,
                            description: repo.description,
                            language: repo.language,
                            stars: repo.stargazers_count,
                            forks: repo.forks_count,
                            html_url: repo.html_url
                        }));

                    return JSON.stringify({
                        total_repositories: repos.length,
                        original_repositories: repos.filter(repo => !repo.fork).length,
                        forked_repositories: repos.filter(repo => repo.fork).length,
                        total_stars: totalStars,
                        total_forks: totalForks,
                        total_size_kb: totalSize,
                        language_breakdown: languageStats,
                        top_repositories: topRepos
                    }, null, 2);
                } catch (error) {
                    console.error('Error in getRepositoryStatistics:', error);
                    throw new Error(`Failed to retrieve repository statistics: ${error.message}`);
                }
            }
        })
    ];
}
