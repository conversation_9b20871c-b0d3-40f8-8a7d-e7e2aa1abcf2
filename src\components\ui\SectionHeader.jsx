import React from 'react';
import { motion } from 'framer-motion';
import { textVariant } from '../../utils/motion';
import { styles } from '../../styles';

const SectionHeader = ({ 
  subtitle, 
  title, 
  description,
  centered = false,
  className = ''
}) => {
  return (
    <motion.div 
      variants={textVariant()} 
      className={`relative z-10 ${centered ? 'text-center' : ''} ${className}`}
    >
      {subtitle && (
        <p className={styles.sectionSubText}>
          {subtitle}
        </p>
      )}
      
      {title && (
        <h2 className={styles.sectionHeadText}>
          {title}
        </h2>
      )}
      
      {description && (
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className={`${styles.bodyText} mt-4 max-w-3xl ${centered ? 'mx-auto' : ''}`}
        >
          {description}
        </motion.p>
      )}
    </motion.div>
  );
};

export default SectionHeader;
