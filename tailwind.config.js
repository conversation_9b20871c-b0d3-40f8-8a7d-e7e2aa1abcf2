/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx}"],
  mode: "jit",
  theme: {
    extend: {
      colors: {
        // Professional color palette
        primary: "#0f172a",        // Slate 900 - Deep professional dark
        secondary: "#64748b",      // Slate 500 - Professional gray
        tertiary: "#1e293b",       // Slate 800 - Slightly lighter dark
        accent: "#3b82f6",         // Blue 500 - Professional blue accent
        "accent-light": "#60a5fa", // Blue 400 - Lighter blue
        "accent-dark": "#1d4ed8",  // Blue 700 - Darker blue

        // Neutral grays
        "gray-50": "#f8fafc",
        "gray-100": "#f1f5f9",
        "gray-200": "#e2e8f0",
        "gray-300": "#cbd5e1",
        "gray-400": "#94a3b8",
        "gray-500": "#64748b",
        "gray-600": "#475569",
        "gray-700": "#334155",
        "gray-800": "#1e293b",
        "gray-900": "#0f172a",

        // Legacy colors for compatibility
        "black-100": "#1e293b",
        "black-200": "#0f172a",
        "white-100": "#f8fafc",

        // Success, warning, error colors
        success: "#10b981",
        warning: "#f59e0b",
        error: "#ef4444",
      },
      boxShadow: {
        card: "0px 20px 60px -15px rgba(15, 23, 42, 0.3)",
        "card-hover": "0px 25px 80px -15px rgba(59, 130, 246, 0.2)",
        professional: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        "professional-lg": "0px 10px 40px rgba(0, 0, 0, 0.15)",
      },
      screens: {
        xs: "450px",
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
      },
      backgroundImage: {
        "hero-pattern": "url('/src/assets/herobg.png')",
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-professional": "linear-gradient(135deg, #0f172a 0%, #1e293b 100%)",
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'display': ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
    },
  },
  plugins: [],
};