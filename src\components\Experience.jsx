import {VerticalTimeline, VerticalTimelineElement} from 'react-vertical-timeline-component';
import {motion} from 'framer-motion';
import 'react-vertical-timeline-component/style.min.css';

import {styles} from '../styles';
import {experiences} from '../constants';
import {textVariant, staggerContainer} from '../utils/motion';


const Experience = () => {
  return (
    <motion.section
      variants={staggerContainer()}
      initial="hidden"
      whileInView="show"
      viewport={{once: true, amount: 0.25}}
      className={`${styles.maxWidth} ${styles.sectionPadding} relative`}
      id="experience"
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-accent/3 rounded-full blur-3xl"></div>

      <motion.div variants={textVariant()} className="relative z-10">
        <p className={styles.sectionSubText}>What I have done so far</p>
        <h2 className={styles.sectionHeadText}>Work Experience.</h2>
      </motion.div>

      <div className='mt-20 flex flex-col relative z-10'>
        <VerticalTimeline>
          {experiences.map((experience, index) => (
            <VerticalTimelineElement
              key={index}
              contentStyle={{
                background: 'linear-gradient(135deg, #1e293b 0%, #334155 100%)',
                color: '#fff',
                boxShadow: '0px 10px 40px rgba(0, 0, 0, 0.15)',
                border: '1px solid rgba(59, 130, 246, 0.1)',
                borderRadius: '16px'
              }}
              contentArrowStyle={{borderRight: '7px solid #334155'}}
              date={experience.date}
              iconStyle={{
                background: experience.iconBg || '#3b82f6',
                boxShadow: '0px 4px 20px rgba(59, 130, 246, 0.3)'
              }}
              icon={
                <div className='flex justify-center items-center w-full h-full'>
                  <img
                    src={experience.icon}
                    alt={experience.company_name}
                    className='w-[60%] h-[60%] object-contain filter brightness-110'
                  />
                </div>
              }
            >
              <div>
                <h3 className='text-white text-xl font-bold mb-2'>{experience.title}</h3>
                <p className='text-accent text-base font-semibold mb-4'>{experience.company_name}</p>
              </div>
              <ul className='mt-5 list-disc ml-5 space-y-3'>
                {experience.points.map((point, index) => (
                  <li
                    key={`experience-point-${index}`}
                    className='text-gray-300 text-sm leading-relaxed pl-1'
                  >
                    {point}
                  </li>
                ))}
              </ul>
            </VerticalTimelineElement>
          ))}
        </VerticalTimeline>
      </div>
    </motion.section>
  )
}

export default Experience