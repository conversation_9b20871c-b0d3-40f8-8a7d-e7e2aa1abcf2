/**
 * GitHub API Service
 * Provides utilities for interacting with GitHub API
 */

const GITHUB_API_BASE = 'https://api.github.com';

// Handle both browser (Vite) and Node.js environments
let GITHUB_TOKEN;
if (typeof import.meta !== 'undefined' && import.meta.env) {
    // Browser environment (Vite)
    GITHUB_TOKEN = import.meta.env.VITE_GITHUB_TOKEN;
} else if (typeof process !== 'undefined' && process.env) {
    // Node.js environment
    GITHUB_TOKEN = process.env.VITE_GITHUB_TOKEN;
} else if (typeof global !== 'undefined' && global.importMeta) {
    // Mock environment for testing
    GITHUB_TOKEN = global.importMeta.env.VITE_GITHUB_TOKEN;
}

if (!GITHUB_TOKEN) {
    console.warn('GitHub token not found in environment variables. Some GitHub features may not work.');
}

/**
 * Base fetch function for GitHub API calls
 * @param {string} endpoint - API endpoint (without base URL)
 * @param {object} options - Fetch options
 * @returns {Promise<object>} - API response
 */
async function githubFetch(endpoint, options = {}) {
    const url = `${GITHUB_API_BASE}${endpoint}`;
    
    const defaultHeaders = {
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'Portfolio-App/1.0'
    };

    if (GITHUB_TOKEN) {
        defaultHeaders['Authorization'] = `Bearer ${GITHUB_TOKEN}`;
    }

    const config = {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        }
    };

    try {
        const response = await fetch(url, config);
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(`GitHub API Error: ${response.status} - ${errorData.message || response.statusText}`);
        }

        return await response.json();
    } catch (error) {
        console.error('GitHub API request failed:', error);
        throw error;
    }
}

/**
 * Get user profile information
 * @param {string} username - GitHub username
 * @returns {Promise<object>} - User profile data
 */
export async function getUserProfile(username = 'shuvo881') {
    try {
        return await githubFetch(`/users/${username}`);
    } catch (error) {
        throw new Error(`Failed to fetch user profile: ${error.message}`);
    }
}

/**
 * Get user's repositories
 * @param {string} username - GitHub username
 * @param {object} options - Query options
 * @returns {Promise<Array>} - Array of repositories
 */
export async function getUserRepositories(username = 'shuvo881', options = {}) {
    try {
        const {
            type = 'all', // all, owner, member
            sort = 'updated', // created, updated, pushed, full_name
            direction = 'desc', // asc, desc
            per_page = 30,
            page = 1
        } = options;

        const queryParams = new URLSearchParams({
            type,
            sort,
            direction,
            per_page: per_page.toString(),
            page: page.toString()
        });

        return await githubFetch(`/users/${username}/repos?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to fetch repositories: ${error.message}`);
    }
}

/**
 * Get repository details
 * @param {string} username - GitHub username
 * @param {string} repoName - Repository name
 * @returns {Promise<object>} - Repository details
 */
export async function getRepository(username = 'shuvo881', repoName) {
    try {
        if (!repoName) {
            throw new Error('Repository name is required');
        }
        return await githubFetch(`/repos/${username}/${repoName}`);
    } catch (error) {
        throw new Error(`Failed to fetch repository: ${error.message}`);
    }
}

/**
 * Get repository languages
 * @param {string} username - GitHub username
 * @param {string} repoName - Repository name
 * @returns {Promise<object>} - Languages used in repository
 */
export async function getRepositoryLanguages(username = 'shuvo881', repoName) {
    try {
        if (!repoName) {
            throw new Error('Repository name is required');
        }
        return await githubFetch(`/repos/${username}/${repoName}/languages`);
    } catch (error) {
        throw new Error(`Failed to fetch repository languages: ${error.message}`);
    }
}

/**
 * Get repository commits
 * @param {string} username - GitHub username
 * @param {string} repoName - Repository name
 * @param {object} options - Query options
 * @returns {Promise<Array>} - Array of commits
 */
export async function getRepositoryCommits(username = 'shuvo881', repoName, options = {}) {
    try {
        if (!repoName) {
            throw new Error('Repository name is required');
        }

        const {
            sha = 'main', // branch or commit SHA
            per_page = 30,
            page = 1,
            since,
            until
        } = options;

        const queryParams = new URLSearchParams({
            sha,
            per_page: per_page.toString(),
            page: page.toString()
        });

        if (since) queryParams.append('since', since);
        if (until) queryParams.append('until', until);

        return await githubFetch(`/repos/${username}/${repoName}/commits?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to fetch repository commits: ${error.message}`);
    }
}

/**
 * Get user's GitHub activity/events
 * @param {string} username - GitHub username
 * @param {object} options - Query options
 * @returns {Promise<Array>} - Array of events
 */
export async function getUserEvents(username = 'shuvo881', options = {}) {
    try {
        const {
            per_page = 30,
            page = 1
        } = options;

        const queryParams = new URLSearchParams({
            per_page: per_page.toString(),
            page: page.toString()
        });

        return await githubFetch(`/users/${username}/events?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to fetch user events: ${error.message}`);
    }
}

/**
 * Get user's followers
 * @param {string} username - GitHub username
 * @param {object} options - Query options
 * @returns {Promise<Array>} - Array of followers
 */
export async function getUserFollowers(username = 'shuvo881', options = {}) {
    try {
        const {
            per_page = 30,
            page = 1
        } = options;

        const queryParams = new URLSearchParams({
            per_page: per_page.toString(),
            page: page.toString()
        });

        return await githubFetch(`/users/${username}/followers?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to fetch followers: ${error.message}`);
    }
}

/**
 * Get user's following
 * @param {string} username - GitHub username
 * @param {object} options - Query options
 * @returns {Promise<Array>} - Array of users being followed
 */
export async function getUserFollowing(username = 'shuvo881', options = {}) {
    try {
        const {
            per_page = 30,
            page = 1
        } = options;

        const queryParams = new URLSearchParams({
            per_page: per_page.toString(),
            page: page.toString()
        });

        return await githubFetch(`/users/${username}/following?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to fetch following: ${error.message}`);
    }
}

/**
 * Search repositories
 * @param {string} query - Search query
 * @param {object} options - Search options
 * @returns {Promise<object>} - Search results
 */
export async function searchRepositories(query, options = {}) {
    try {
        if (!query) {
            throw new Error('Search query is required');
        }

        const {
            sort = 'stars', // stars, forks, help-wanted-issues, updated
            order = 'desc', // asc, desc
            per_page = 30,
            page = 1
        } = options;

        const queryParams = new URLSearchParams({
            q: query,
            sort,
            order,
            per_page: per_page.toString(),
            page: page.toString()
        });

        return await githubFetch(`/search/repositories?${queryParams}`);
    } catch (error) {
        throw new Error(`Failed to search repositories: ${error.message}`);
    }
}
