import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from "react-router-dom";
import { styles } from "../styles";
import { navLinks } from "../constants";
import { logo, menu, close } from "../assets";

const Navbar = () => {
  const [active, setActive] = useState("");
  const [toggle, setToggle] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogoClick = (e) => {
    e.preventDefault();
    
    if (location.pathname === '/') {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      navigate('/');
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }, 100);
    }
    
    setActive("");
  };

  return (
    <nav
      className={`${styles.paddingX} w-full flex items-center py-4 fixed top-0 z-20 bg-primary/95 backdrop-blur-md border-b border-gray-800/50 transition-all duration-300`}
    >
      <div className="w-full flex justify-between items-center max-w-7xl mx-auto">
        <Link
          to="/"
          className="flex items-center gap-3 group"
          onClick={handleLogoClick}
        >
          <div className="relative">
            <img
              src={logo}
              alt="logo"
              className="w-10 h-10 object-contain transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-accent/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
          <div className="flex flex-col">
            <h1 className="text-white text-lg font-bold cursor-pointer transition-colors duration-300 group-hover:text-accent">
              Md. Golam Mostofa
            </h1>
            <p className="text-gray-400 text-xs font-medium cursor-pointer transition-colors duration-300 group-hover:text-gray-300">
              AI Engineer & Team Leader
            </p>
          </div>
        </Link>

        <ul className="list-none hidden sm:flex flex-row gap-8">
          {navLinks.map((link) => (
            <li
              key={link.id}
              className="relative group"
              onClick={() => setActive(link.title)}
            >
              <Link
                to={`/${link.id}`}
                className={`${
                  active === link.title
                    ? "text-accent"
                    : "text-gray-300 hover:text-white"
                } text-sm font-medium cursor-pointer transition-all duration-300 py-2 px-3 rounded-lg hover:bg-gray-800/50`}
              >
                {link.title}
              </Link>
              {active === link.title && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-accent rounded-full"></div>
              )}
            </li>
          ))}
        </ul>

        <div className="sm:hidden flex flex-1 justify-end items-center">
          <button
            className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-gray-800/50 transition-colors duration-300"
            onClick={() => setToggle(!toggle)}
            aria-label="Toggle menu"
          >
            <img
              src={toggle ? close : menu}
              alt="menu"
              className="w-6 h-6 object-contain"
            />
          </button>
          <div
            className={`${
              !toggle ? "hidden" : "flex"
            } p-6 bg-gray-900/95 backdrop-blur-md border border-gray-800/50 absolute top-16 right-0 mx-4 my-2 min-w-[200px] z-10 rounded-xl shadow-professional-lg`}
          >
            <ul className="list-none flex justify-end items-start flex-col gap-3 w-full">
              {navLinks.map((link) => (
                <li
                  key={link.id}
                  className="w-full"
                  onClick={() => {
                    setToggle(!toggle);
                    setActive(link.title);
                  }}
                >
                  <Link
                    to={`/${link.id}`}
                    className={`${
                      active === link.title
                        ? "text-accent bg-accent/10"
                        : "text-gray-300 hover:text-white hover:bg-gray-800/50"
                    } font-medium cursor-pointer text-sm py-2 px-3 rounded-lg transition-all duration-300 block w-full`}
                  >
                    {link.title}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;