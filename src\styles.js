const styles = {
  // Layout and spacing
  paddingX: "sm:px-16 px-6",
  paddingY: "sm:py-20 py-12",
  padding: "sm:px-16 px-6 sm:py-20 py-12",

  // Container styles
  maxWidth: "max-w-7xl mx-auto px-6 sm:px-8 lg:px-16",
  sectionPadding: "py-12 sm:py-16 lg:py-20",

  // Professional typography
  heroHeadText:
    "text-professional-display text-white text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight",
  heroSubText:
    "text-professional text-gray-300 text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed mt-4",

  sectionHeadText:
    "text-professional-heading text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4",
  sectionSubText:
    "text-professional text-accent uppercase tracking-wider text-xs sm:text-sm font-medium mb-6 sm:mb-8",

  // Card and component styles
  cardPadding: "p-8",
  cardRadius: "rounded-2xl",
  cardShadow: "shadow-professional",

  // Button styles
  buttonPrimary: "bg-accent hover:bg-accent-dark text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 shadow-professional hover:shadow-professional-lg",
  buttonSecondary: "border-2 border-accent text-accent hover:bg-accent hover:text-white font-medium py-3 px-6 rounded-lg transition-all duration-300",

  // Text styles
  bodyText: "text-professional text-gray-300 leading-relaxed",
  captionText: "text-professional text-gray-400 text-sm",

  // Spacing utilities
  sectionGap: "space-y-12",
  cardGap: "gap-8",
};

export { styles };