import React, { Suspense } from 'react';
import { BrowserRouter, Routes, Route } from "react-router-dom";
import {
  About,
  Contact,
  Experience,
  Feedbacks,
  Hero,
  Navbar,
  Tech,
  Works,
  StarsCanvas,
  Publications,
  Education,
  Photos,
  Bootcamps,
  FloatingChat,
  AnimatedChatIcon,
} from './components';
import RouterError from './RouterError';

// Loading component
const LoadingSpinner = () => (
  <div className="flex flex-col items-center justify-center h-screen bg-gradient-professional">
    <div className="relative">
      <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-600 border-t-accent" />
      <div className="absolute inset-0 rounded-full bg-accent/20 blur-lg animate-pulse" />
    </div>
    <p className="mt-4 text-gray-400 text-sm font-medium">Loading...</p>
  </div>
);

// Main content for home page
const MainContent = () => (
  <>
    <div className="bg-hero-pattern bg-cover bg-no-repeat bg-center">
      <Hero />
    </div>
    <About />
    <Experience />
    <Tech />
    <Education />
    <Works />
    <Publications />
    <Feedbacks />
    <div className="relative z-0">
      <Contact />
      <StarsCanvas />
    </div>
  </>
);

// Works page content
const WorksContent = () => (
  <>
    <Experience />
    <Tech />
  </>
);

// Contact page content
const ContactContent = () => (
  <div className="relative z-0">
    <Contact />
    <StarsCanvas />
  </div>
);

const App = () => {

  return (
    <BrowserRouter>
      <div className="relative z-0 bg-gradient-professional min-h-screen">
        <Navbar />
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            <Route path="/" element={<MainContent />} />
            <Route path="/photos" element={<Photos />} />
            <Route path="/about" element={<About />} />
            <Route path="/works" element={<WorksContent />} />
            <Route path="/educations" element={<Education />} />
            <Route path="/projects" element={<Works />} />
            <Route path="/publications" element={<Publications />} />
            <Route path="/bootcamps" element={<Bootcamps />} />
            <Route path="/contact" element={<ContactContent />} />
            <Route path="*" element={<RouterError />} />
          </Routes>
        </Suspense>
        <FloatingChat />
      </div>
    </BrowserRouter>
  );
};

export default App;