import React from 'react';
import { motion } from 'framer-motion';

const ProfessionalButton = ({ 
  children, 
  variant = 'primary', 
  size = 'md',
  className = '',
  disabled = false,
  loading = false,
  icon,
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-accent/50 disabled:opacity-50 disabled:cursor-not-allowed';
  
  const variants = {
    primary: 'bg-accent hover:bg-accent-dark text-white shadow-professional hover:shadow-professional-lg hover:scale-105',
    secondary: 'border-2 border-accent text-accent hover:bg-accent hover:text-white',
    outline: 'border border-gray-600 text-gray-300 hover:bg-gray-800/50 hover:border-gray-500',
    ghost: 'text-gray-300 hover:bg-gray-800/50 hover:text-white',
    success: 'bg-success hover:bg-success/90 text-white shadow-professional',
    warning: 'bg-warning hover:bg-warning/90 text-white shadow-professional',
    error: 'bg-error hover:bg-error/90 text-white shadow-professional'
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    xl: 'px-8 py-4 text-xl'
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;

  return (
    <motion.button
      className={classes}
      disabled={disabled || loading}
      whileHover={{ scale: disabled ? 1 : 1.02 }}
      whileTap={{ scale: disabled ? 1 : 0.98 }}
      {...props}
    >
      {loading && (
        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
      )}
      {icon && !loading && (
        <span className="mr-2">{icon}</span>
      )}
      {children}
    </motion.button>
  );
};

export default ProfessionalButton;
