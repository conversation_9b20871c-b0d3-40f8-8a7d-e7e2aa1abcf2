
import React from 'react';
import { motion } from 'framer-motion';
import { technologies } from "../constants";
import { styles } from '../styles';
import { textVariant, staggerContainer, fadeIn } from '../utils/motion';

const TechCard = ({ technology, index }) => (
  <motion.div
    variants={fadeIn("up", "spring", index * 0.1, 0.75)}
    className="w-32 h-32 relative group cursor-pointer"
  >
    <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-tertiary to-gray-800
      border border-gray-700/50 backdrop-blur-sm transform transition-all duration-500 ease-out
      group-hover:scale-110 group-hover:border-accent/30 group-hover:shadow-card-hover
      group-hover:from-gray-800 group-hover:to-tertiary">

      {/* Background glow effect */}
      <div className="absolute inset-0 rounded-2xl bg-accent/5 opacity-0 group-hover:opacity-100
        transition-opacity duration-500 blur-lg"></div>

      <div className="relative w-full h-full flex flex-col items-center justify-center p-4">
        <img
          src={technology.icon}
          alt={technology.name}
          className="w-12 h-12 object-contain transition-all duration-500
          group-hover:scale-110 filter brightness-110"
        />
        <p className="mt-3 text-xs text-gray-400 text-center font-medium
          opacity-0 group-hover:opacity-100 transition-all duration-500
          group-hover:text-accent-light transform translate-y-2 group-hover:translate-y-0">
          {technology.name}
        </p>
      </div>
    </div>
  </motion.div>
);

const Tech = () => {
  return (
    <motion.section
      variants={staggerContainer()}
      initial="hidden"
      whileInView="show"
      viewport={{once: true, amount: 0.25}}
      className={`${styles.maxWidth} ${styles.sectionPadding} relative`}
      id="tech"
    >
      {/* Background decoration */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-accent/3 rounded-full blur-3xl"></div>

      <motion.div variants={textVariant()} className="text-center relative z-10 mb-16">
        <p className={styles.sectionSubText}>Technologies I work with</p>
        <h2 className={styles.sectionHeadText}>Tech Stack.</h2>
      </motion.div>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-8 relative z-10">
        {technologies.map((technology, index) => (
          <TechCard
            key={technology.name}
            technology={technology}
            index={index}
          />
        ))}
      </div>
    </motion.section>
  );
};

export default Tech