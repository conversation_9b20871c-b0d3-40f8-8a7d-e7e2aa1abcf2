import React from 'react';
import { motion } from 'framer-motion';
import { fadeIn } from '../../utils/motion';

const ProfessionalCard = ({ 
  children, 
  className = '', 
  index = 0, 
  hover = true,
  gradient = false,
  padding = 'p-6',
  ...props 
}) => {
  const baseClasses = `
    bg-gradient-to-br from-tertiary to-gray-800 
    rounded-2xl 
    shadow-professional 
    border border-gray-700/50
    transition-all duration-500
    ${padding}
    ${hover ? 'hover:shadow-card-hover hover:border-accent/30 hover:from-gray-800 hover:to-tertiary' : ''}
    ${gradient ? 'professional-gradient-subtle' : ''}
    ${className}
  `;

  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.2, 0.75)}
      className={baseClasses}
      {...props}
    >
      {children}
    </motion.div>
  );
};

export default ProfessionalCard;
