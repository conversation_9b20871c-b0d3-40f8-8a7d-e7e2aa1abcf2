import React from 'react';
import { motion } from 'framer-motion';
import { styles } from '../styles';
import { github } from '../assets';
import { browser } from '../assets';
import { projects } from '../constants';
import { Tilt } from 'react-tilt';
import { textVariant, staggerContainer, fadeIn } from '../utils/motion';

const ProjectCard = ({ name, description, tags, image, source_code_link, index }) => {
  return (
    <motion.div
      variants={fadeIn("up", "spring", index * 0.2, 0.75)}
      className="w-full group"
    >
      <Tilt
        options={{
          max: 25,
          scale: 1.02,
          speed: 450
        }}
        className="bg-gradient-to-br from-tertiary to-gray-800 p-6 rounded-2xl h-full
        border border-gray-700/50 shadow-professional hover:shadow-card-hover
        hover:border-accent/30 transition-all duration-500 hover:from-gray-800 hover:to-tertiary"
      >
        <div className="relative w-full h-[240px] overflow-hidden rounded-xl">
          <img
            src={image}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent
            opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

          <div className="absolute inset-0 flex justify-end items-start m-4">
            <motion.div
              onClick={() => window.open(source_code_link, "_blank")}
              className="w-12 h-12 rounded-full bg-accent/90 backdrop-blur-sm flex justify-center items-center
              cursor-pointer shadow-lg hover:bg-accent transition-all duration-300 hover:scale-110"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <img
                src={source_code_link && source_code_link.includes('github') ? github : browser}
                alt="source code"
                className="w-6 h-6 object-contain filter brightness-0 invert"
              />
            </motion.div>
          </div>
        </div>

        <div className="mt-6">
          <h3 className="text-white font-bold text-xl mb-3 group-hover:text-accent-light transition-colors duration-300">
            {name}
          </h3>
          <p className="text-gray-300 text-sm leading-relaxed">
            {description}
          </p>
        </div>

        <div className="mt-6 flex flex-wrap gap-2">
          {tags.map((tag) => (
            <span
              key={tag.name}
              className={`text-xs px-3 py-1 rounded-full bg-gray-800/50 border border-gray-600/50
              ${tag.color} font-medium transition-all duration-300 hover:border-accent/50`}
            >
              #{tag.name}
            </span>
          ))}
        </div>
      </Tilt>
    </motion.div>
  );
};

const Works = () => {
  return (
    <motion.section
      variants={staggerContainer()}
      initial="hidden"
      whileInView="show"
      viewport={{once: true, amount: 0.25}}
      className={`${styles.maxWidth} ${styles.sectionPadding} relative`}
      id="projects"
    >
      {/* Background decoration */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-accent/3 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-72 h-72 bg-accent/2 rounded-full blur-3xl"></div>

      <motion.div variants={textVariant()} className="relative z-10">
        <p className={styles.sectionSubText}>My work</p>
        <h2 className={styles.sectionHeadText}>Projects.</h2>
      </motion.div>

      <motion.div
        variants={fadeIn("", "", 0.1, 1)}
        className="w-full relative z-10"
      >
        <p className={`mt-4 ${styles.bodyText} max-w-4xl leading-relaxed`}>
          These projects exemplify my skills and experience through real-world applications of my work.
          Each project is concisely presented with links to its code repository and live demo,
          offering insight into my ability to tackle complex challenges, adapt to various technologies,
          and manage projects efficiently from inception to completion.
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mt-20 relative z-10">
        {projects.map((project, index) => (
          <ProjectCard
            key={`project-${index}`}
            index={index}
            {...project}
          />
        ))}
      </div>
    </motion.section>
  );
};

export default Works;