import React from 'react';
import { styles } from '../../styles';

const ProfessionalContainer = ({ 
  children, 
  className = '',
  background = true,
  padding = true,
  maxWidth = true,
  decoration = false
}) => {
  const containerClasses = `
    relative
    ${maxWidth ? styles.maxWidth : ''}
    ${padding ? styles.sectionPadding : ''}
    ${className}
  `;

  return (
    <section className={containerClasses}>
      {/* Background decoration */}
      {decoration && (
        <>
          <div className="absolute top-0 left-0 w-72 h-72 bg-accent/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent/3 rounded-full blur-3xl"></div>
        </>
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </section>
  );
};

export default ProfessionalContainer;
