import { Tilt } from 'react-tilt';
import React from 'react';
import { motion } from 'framer-motion';
import { styles } from '../styles';
import { services } from '../constants';
import { fadeIn, textVariant, staggerContainer } from '../utils/motion';

// ServiceCard component
const ServiceCard = ({ index, title, icon }) => (
  <Tilt className='xs:w-[280px] w-full group'>
    <motion.div
      variants={fadeIn("right", "spring", 0.3 * index, 0.75)}
      className='w-full professional-gradient-subtle p-[1px] rounded-2xl shadow-professional hover:shadow-card-hover transition-all duration-500'
    >
      <div className='bg-gradient-to-br from-tertiary to-gray-800 rounded-2xl py-8 px-8 min-h-[300px] flex justify-center items-center flex-col relative overflow-hidden group-hover:from-gray-800 group-hover:to-tertiary transition-all duration-500'>
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-20 h-20 bg-accent/5 rounded-full blur-xl group-hover:bg-accent/10 transition-all duration-500"></div>

        <div className="relative z-10 flex flex-col items-center">
          <div className="w-20 h-20 rounded-xl bg-accent/10 flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-all duration-500 group-hover:scale-110">
            <img
              src={icon}
              alt={title}
              className='w-12 h-12 object-contain filter brightness-110'
            />
          </div>
          <h3 className='text-white text-xl font-semibold text-center leading-tight group-hover:text-accent-light transition-colors duration-300'>
            {title}
          </h3>
        </div>
      </div>
    </motion.div>
  </Tilt>
);

// About component
const About = () => {
  return (
    <motion.section
      variants={staggerContainer()}
      initial="hidden"
      whileInView="show"
      viewport={{once: true, amount: 0.25}}
      className={`${styles.maxWidth} ${styles.sectionPadding} relative`}
      id="about"
    >
      {/* Background decoration */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-accent/5 rounded-full blur-3xl"></div>

      <motion.div variants={textVariant()} className="relative z-10">
        <p className={styles.sectionSubText}>Introduction</p>
        <h2 className={styles.sectionHeadText}>Overview.</h2>
      </motion.div>

      <div className="relative z-10 space-y-6 mt-8">
        <motion.p
          variants={fadeIn("", "", 0.1, 1)}
          className={`${styles.bodyText} text-lg leading-relaxed max-w-4xl`}
        >
          Hi, I'm <span className="text-white font-semibold">Md. Golam Mostofa</span>, an experienced AI Engineer and Team Leader with <span className="text-accent font-semibold">2+ years</span> of expertise in building production-ready Large Language Models, AI agents, and scalable backend systems. Currently leading a team of 3 engineers while architecting end-to-end automation solutions using Microsoft Power Platform.
        </motion.p>

        <motion.p
          variants={fadeIn("", "", 0.2, 1)}
          className={`${styles.bodyText} leading-relaxed max-w-4xl`}
        >
          At <span className="text-accent font-semibold">Devolved AI</span>, I specialize in fine-tuning <span className="text-white font-semibold">Large Language Models (LLMs)</span> and deploying high-performance applications using <span className="text-white font-semibold">Django</span>, <span className="text-white font-semibold">REST APIs</span>, and <span className="text-white font-semibold">Docker</span>. I have a proven track record in LLM fine-tuning, RAG implementation, and deploying AI-powered applications that drive measurable business impact.
        </motion.p>

        <motion.p
          variants={fadeIn("", "", 0.3, 1)}
          className={`${styles.bodyText} leading-relaxed max-w-4xl`}
        >
          I have deep experience with technologies like <span className="text-white font-semibold">PyTorch</span>, <span className="text-white font-semibold">TensorFlow</span>, and <span className="text-white font-semibold">NLP</span>, along with strong backend deployment skills using <span className="text-white font-semibold">AWS EC2</span> and <span className="text-white font-semibold">PostgreSQL</span>. My key projects include <span className="text-accent font-semibold">Athena LLM</span> and an <span className="text-accent font-semibold">AI Chat Bot</span>, where I combined advanced AI models with robust backend infrastructures.
        </motion.p>

        <motion.div
          variants={fadeIn("", "", 0.4, 1)}
          className="flex flex-wrap gap-4 mt-8"
        >
          <a
            href="https://dl.acm.org/doi/10.1145/3605098.3636188"
            className="inline-flex items-center px-4 py-2 bg-accent/10 hover:bg-accent/20 text-accent border border-accent/30 rounded-lg transition-all duration-300 hover:scale-105"
            target="_blank"
            rel="noopener noreferrer"
          >
            📄 Published Research
          </a>
          <a
            href="https://leetcode.com/u/golammostofa10001/"
            className="inline-flex items-center px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 border border-gray-600 rounded-lg transition-all duration-300 hover:scale-105"
            target="_blank"
            rel="noopener noreferrer"
          >
            💻 LeetCode Profile
          </a>
          <a
            href="https://www.kaggle.com/golammostofas"
            className="inline-flex items-center px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 border border-gray-600 rounded-lg transition-all duration-300 hover:scale-105"
            target="_blank"
            rel="noopener noreferrer"
          >
            🏆 Kaggle Profile
          </a>
          <a
            href="https://scholar.google.com/citations?user=mxW1qOoAAAAJ"
            className="inline-flex items-center px-4 py-2 bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 border border-gray-600 rounded-lg transition-all duration-300 hover:scale-105"
            target="_blank"
            rel="noopener noreferrer"
          >
            🎓 Google Scholar
          </a>
        </motion.div>

        <motion.div
          variants={fadeIn("", "", 0.5, 1)}
          className="mt-8 p-6 bg-gradient-to-r from-accent/5 to-transparent border-l-4 border-accent rounded-lg"
        >
          <p className={`${styles.bodyText} leading-relaxed`}>
            <span className="text-accent font-semibold">Let's connect!</span> Feel free to reach out at{' '}
            <a href="mailto:<EMAIL>" className="text-accent hover:text-accent-light underline transition-colors duration-300">
              <EMAIL>
            </a>{' '}
            or on{' '}
            <a href="https://www.linkedin.com/in/mdgolammostofa705/" className="text-accent hover:text-accent-light underline transition-colors duration-300" target="_blank" rel="noopener noreferrer">
              LinkedIn
            </a>.
          </p>
        </motion.div>
      </div>

      <motion.div
        variants={fadeIn("", "", 0.6, 1)}
        className='mt-20 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10'
      >
        {services.map((service, index) => (
          <ServiceCard key={service.title} index={index} {...service} />
        ))}
      </motion.div>
    </motion.section>
  );
};

export default About;
